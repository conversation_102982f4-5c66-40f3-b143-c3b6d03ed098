name: Tests

on:
  push:
    branches:
      - master
      - '*.x'
  pull_request:
  schedule:
    - cron: '0 0 * * *'

permissions:
  contents: read

jobs:
  tests:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: true
      matrix:
        php: [8.1, 8.2, 8.3]

    name: PHP ${{ matrix.php }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite
          coverage: none

      - name: Install Composer dependencies
        run: composer install --prefer-dist --no-interaction --no-progress

      - name: Copy environment file
        run: cp .env.example .env

      - name: Generate app key
        run: php artisan key:generate

      - name: Execute tests
        run: vendor/bin/phpunit
